// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {Platform} from 'react-native';

interface PerformanceMetrics {
    startTime: number;
    endTime?: number;
    duration?: number;
    operation: string;
    deviceInfo?: string;
}

class EmojiPerformanceMonitor {
    private metrics: PerformanceMetrics[] = [];
    private isRedmiDevice: boolean;

    constructor() {
        // Simple detection for Redmi devices (can be enhanced with device info libraries)
        this.isRedmiDevice = Platform.OS === 'android' && 
            (Platform.constants?.Brand?.toLowerCase().includes('redmi') || 
             Platform.constants?.Manufacturer?.toLowerCase().includes('xiaomi'));
    }

    startOperation(operation: string): string {
        const operationId = `${operation}_${Date.now()}`;
        const metric: PerformanceMetrics = {
            startTime: performance.now(),
            operation: operationId,
            deviceInfo: this.isRedmiDevice ? 'Redmi' : 'Other',
        };
        
        this.metrics.push(metric);
        return operationId;
    }

    endOperation(operationId: string): number | null {
        const metric = this.metrics.find(m => m.operation === operationId);
        if (!metric) {
            return null;
        }

        metric.endTime = performance.now();
        metric.duration = metric.endTime - metric.startTime;

        // Log performance warnings for Redmi devices
        if (this.isRedmiDevice && metric.duration > 100) {
            console.warn(`[EmojiPerformance] Slow operation on Redmi device: ${operationId} took ${metric.duration.toFixed(2)}ms`);
        }

        return metric.duration;
    }

    getMetrics(): PerformanceMetrics[] {
        return this.metrics.filter(m => m.duration !== undefined);
    }

    getAverageTime(operation: string): number {
        const operationMetrics = this.metrics.filter(m => 
            m.operation.startsWith(operation) && m.duration !== undefined
        );
        
        if (operationMetrics.length === 0) {
            return 0;
        }

        const totalTime = operationMetrics.reduce((sum, m) => sum + (m.duration || 0), 0);
        return totalTime / operationMetrics.length;
    }

    isPerformanceAcceptable(operation: string, maxDuration: number = 100): boolean {
        const avgTime = this.getAverageTime(operation);
        return avgTime <= maxDuration;
    }

    clearMetrics(): void {
        this.metrics = [];
    }

    logSummary(): void {
        const operations = [...new Set(this.metrics.map(m => m.operation.split('_')[0]))];
        
        console.log('[EmojiPerformance] Performance Summary:');
        operations.forEach(op => {
            const avgTime = this.getAverageTime(op);
            const status = this.isPerformanceAcceptable(op) ? '✅' : '⚠️';
            console.log(`  ${status} ${op}: ${avgTime.toFixed(2)}ms avg`);
        });
    }
}

export const emojiPerformanceMonitor = new EmojiPerformanceMonitor();

// Helper functions for common operations
export const measureEmojiSelection = () => 
    emojiPerformanceMonitor.startOperation('emoji_selection');

export const measureEmojiPreview = () => 
    emojiPerformanceMonitor.startOperation('emoji_preview');

export const measureTextInputUpdate = () => 
    emojiPerformanceMonitor.startOperation('text_input_update');

export const measureAnimationPerformance = () => 
    emojiPerformanceMonitor.startOperation('animation');

export const measureBottomSheetResize = () => 
    emojiPerformanceMonitor.startOperation('bottom_sheet_resize');
