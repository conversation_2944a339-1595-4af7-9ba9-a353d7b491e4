// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback, useState, useMemo, useEffect} from 'react';
import {DeviceEventEmitter, StyleSheet, Dimensions, Animated} from 'react-native';

import {Events} from '@constants';
import {useIsTablet} from '@hooks/device';
import {useKeyboardHeight} from '@hooks/device';
import BottomSheet from '@screens/bottom_sheet';
import {getEmojiByName} from '@utils/emoji/helpers';
import {measureEmojiSelection, measureEmojiPreview, emojiPerformanceMonitor} from '@utils/emoji_performance_monitor';

import Picker from './picker';
import PickerFooter from './picker/footer';

import type {AvailableScreens} from '@typings/screens/navigation';
import type {SelectedEmoji} from '@components/post_draft/emoji_preview';

type Props = {
    componentId: AvailableScreens;
    onEmojiPress: (emoji: string) => void;
    imageUrl?: string;
    file?: ExtractedFileInfo;
    closeButtonId: string;
    isSelectingMultiple?: boolean | undefined;
    onClose?: (selectedEmojis: SelectedEmoji[]) => void;
    onEmojiSelectionChange?: (selectedEmojis: SelectedEmoji[]) => void;
};

const style = StyleSheet.create({
    contentStyle: {
        paddingTop: 14,
    },
});

const EmojiPickerScreen = ({closeButtonId, componentId, file, imageUrl, onEmojiPress, isSelectingMultiple = false, onClose, onEmojiSelectionChange}: Props) => {
    const isTablet = useIsTablet();
    const keyboardHeight = useKeyboardHeight();
    const [selectedEmojis, setSelectedEmojis] = useState<SelectedEmoji[]>([]);

    // Animation for smooth emoji selection feedback
    const selectionScale = useRef(new Animated.Value(1)).current;

    // Calculate snap points based on keyboard height
    const snapPoints = useMemo(() => {
        if (isTablet) {
            return [1, '50%', '80%']; // Default for tablets
        }

        const screenHeight = Dimensions.get('window').height;

        if (keyboardHeight > 0) {
            // When keyboard is visible, match its height
            const keyboardHeightPercentage = Math.min((keyboardHeight / screenHeight) * 100, 80);
            return [1, `${keyboardHeightPercentage}%`, '80%'];
        }

        // Default behavior when keyboard is not visible
        return [1, '50%', '80%'];
    }, [isTablet, keyboardHeight]);

    const handleEmojiPress = useCallback((emoji: string) => {
        const selectionId = measureEmojiSelection();

        if (isSelectingMultiple) {
            // For multiple selection, add to selected emojis instead of calling onEmojiPress immediately
            const emojiDataMew = getEmojiByName(emoji, []);
            let emojiCharacter = `:${emoji}:`;

            if (emojiDataMew?.image && emojiDataMew.category !== 'custom') {
                const codeArray: string[] = emojiDataMew.image.split('-');
                const code = codeArray.reduce((acc, c) => {
                    return acc + String.fromCodePoint(parseInt(c, 16));
                }, '');
                emojiCharacter = code;
            }

            const newEmoji: SelectedEmoji = {
                id: `${emoji}_${Date.now()}`,
                character: emojiCharacter,
                name: emoji,
            };

            // Add subtle selection feedback animation
            Animated.sequence([
                Animated.timing(selectionScale, {
                    toValue: 1.05,
                    duration: 100,
                    useNativeDriver: true,
                }),
                Animated.timing(selectionScale, {
                    toValue: 1,
                    duration: 100,
                    useNativeDriver: true,
                }),
            ]).start();

            setSelectedEmojis((prev) => {
                const previewId = measureEmojiPreview();
                const newSelectedEmojis = [...prev, newEmoji];
                // Call real-time callback for preview
                onEmojiSelectionChange?.(newSelectedEmojis);
                emojiPerformanceMonitor.endOperation(previewId);
                return newSelectedEmojis;
            });
        } else {
            // For single selection, use original behavior
            onEmojiPress(emoji);
            DeviceEventEmitter.emit(Events.CLOSE_BOTTOM_SHEET);
        }

        emojiPerformanceMonitor.endOperation(selectionId);
    }, [isSelectingMultiple, onEmojiPress, onEmojiSelectionChange]);

    const handleRemoveEmoji = useCallback((id: string) => {
        setSelectedEmojis((prev) => {
            const newSelectedEmojis = prev.filter((emoji) => emoji.id !== id);
            // Call real-time callback for preview
            onEmojiSelectionChange?.(newSelectedEmojis);
            return newSelectedEmojis;
        });
    }, [onEmojiSelectionChange]);

    const handleDone = useCallback(() => {
        onClose?.(selectedEmojis);
        DeviceEventEmitter.emit(Events.CLOSE_BOTTOM_SHEET);
    }, [selectedEmojis, onClose]);

    const renderContent = useCallback(() => {
        return (
            <Animated.View style={{ transform: [{ scale: selectionScale }] }}>
                <Picker
                    onEmojiPress={handleEmojiPress}
                    imageUrl={imageUrl}
                    file={file}
                    testID='emoji_picker'
                    selectedEmojis={selectedEmojis}
                    onRemoveEmoji={handleRemoveEmoji}
                    onDone={handleDone}
                    showPreview={isSelectingMultiple}
                />
            </Animated.View>
        );
    }, [handleEmojiPress, imageUrl, file, selectedEmojis, handleRemoveEmoji, handleDone, isSelectingMultiple, selectionScale]);

    const renderFooter = useCallback((props: any) => {
        if (isTablet) {
            return undefined;
        }
        return (
            <PickerFooter
                {...props}
            />
        );
    }, [isTablet]);

    return (
        <BottomSheet
            renderContent={renderContent}
            closeButtonId={closeButtonId}
            componentId={componentId}
            contentStyle={style.contentStyle}
            initialSnapIndex={1}
            footerComponent={renderFooter}
            snapPoints={snapPoints}
            testID='post_options'
        />
    );
};

export default EmojiPickerScreen;
