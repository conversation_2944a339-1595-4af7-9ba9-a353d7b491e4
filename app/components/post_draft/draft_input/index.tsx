// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useMemo, useRef, useState, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { type LayoutChangeEvent, Platform, ScrollView, TouchableOpacity, View, Animated, DeviceEventEmitter } from 'react-native';
import { type Edge, SafeAreaView } from 'react-native-safe-area-context';

import { General, Events } from '@constants';
import { MENTIONS_REGEX } from '@constants/autocomplete';
import { PostPriorityType } from '@constants/post';
import { useServerUrl } from '@context/server';
import { useTheme } from '@context/theme';
import { persistentNotificationsConfirmation_custom } from '@utils/post';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { type SelectedEmoji } from '../emoji_preview';
import PostInput from '../post_input';
import * as QuickActinoHOlder from '../quick_actions';
import SendAction from '../send_action';
import Typing from '../typing';
import Uploads from '../uploads';

import Header from './header';

import type { PasteInputRef } from '@mattermost/react-native-paste-input';
import { useIsTablet, useKeyboardHeight } from '@app/hooks/device';
import { preventDoubleTap } from '@app/utils/tap';

import { Screens } from '@app/constants';
import { bottomSheet, openAsBottomSheet, showModal } from '@app/screens/navigation';
import CompassIcon from '@components/compass_icon';
import { getEmojiByName } from '@utils/emoji/helpers';
import { isTypeDMorGM } from '@app/utils/channel';

import { getCallsConfig } from '@app/products/calls/state';

import { SEPARATOR_HEIGHT } from '@app/screens/home/<USER>/categories_list/header/plus_menu/separator';
import { bottomSheetSnapPoint } from '@app/utils/helpers';
import { ITEM_HEIGHT } from '@app/components/option_item';

import CompassIcon from '@app/components/compass_icon';
import QuickActions from '../../../screens/channel/header/quick_actions';
import type { SharedValue } from 'react-native-reanimated';

import { getEmojiByName } from '@app/utils/emoji/helpers';
import { useAlert } from '@app/context/alert';
import type { UserModel } from '@app/database/models/server';


type Props = {

    testID?: string;
    channelId: string;
    channelType?: ChannelType;
    channelName?: string;
    rootId?: string;
    currentUserId: string;
    canShowPostPriority?: boolean;

    // Post Props
    postPriority: PostPriority;
    updatePostPriority: (postPriority: PostPriority) => void;
    persistentNotificationInterval: number;
    persistentNotificationMaxRecipients: number;

    // Cursor Position Handler
    updateCursorPosition: React.Dispatch<React.SetStateAction<number>>;
    cursorPosition: number;

    // Send Handler
    sendMessage: () => void;
    canSend: boolean;
    maxMessageLength: number;

    // Draft Handler
    files: FileInfo[];
    fileNotBelongToChannel: FileInfo[];
    value: string;
    uploadFileError: React.ReactNode;
    updateValue: React.Dispatch<React.SetStateAction<string>>;
    addFiles: (files: FileInfo[]) => void;
    updatePostInputTop: (top: number) => void;
    setIsFocused: (isFocused: boolean) => void;
    groupCallsAllowed?: boolean | undefined
    ; translationY?: SharedValue<number> | undefined
    ; isVisibale?: SharedValue<boolean> | undefined;
    pageName: string
    currentUser: UserModel;


}

const SAFE_AREA_VIEW_EDGES: Edge[] = ['left', 'right'];

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return {
        actionsContainer: {
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingBottom: Platform.select({
                ios: 1,
                android: 2,
            }),

        },
        inputContainer: {
            flex: 1,
            flexDirection: 'column',

        },
        inputContentContainer: {
            alignItems: 'stretch',
            paddingTop: Platform.select({
                ios: 7,
                android: 0,
            }),
        },
        inputWrapper: {

            paddingHorizontal: 7,
            alignItems: 'flex-end',
            flexDirection: 'row',
            justifyContent: 'center',
            paddingBottom: 10,
            backgroundColor: theme.centerChannelBg,
            borderWidth: 1,
            borderBottomWidth: 1,
            borderColor: changeOpacity(theme.centerChannelColor, 0.20),
            borderRadius: 12,

            //  borderTopRightRadius: 12,

        },
        postPriorityLabel: {
            marginLeft: 12,
            marginTop: Platform.select({
                ios: 3,
                android: 10,
            }),
        },

        textArea: {
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 7
        }
    };
});

export default function DraftInput({

    testID,
    channelId,
    channelType,
    channelName,
    currentUserId,
    canShowPostPriority,
    files,
    maxMessageLength,
    rootId = '',
    value,
    uploadFileError,
    sendMessage,
    canSend,
    updateValue,
    addFiles,
    updateCursorPosition,
    cursorPosition,
    updatePostInputTop,
    postPriority,
    updatePostPriority,
    persistentNotificationInterval,
    persistentNotificationMaxRecipients,
    setIsFocused,
    groupCallsAllowed = false,
    translationY = undefined,
    isVisibale = undefined,
    pageName,
    currentUser,
    fileNotBelongToChannel
}: Props) {
    const intl = useIntl();
    const serverUrl = useServerUrl();
    const theme = useTheme();
    const keyboardHeight = useKeyboardHeight();

    // Dynamic positioning for emoji picker
    const [emojiPickerVisible, setEmojiPickerVisible] = useState(false);
    const translateY = useRef(new Animated.Value(0)).current;

    const handleLayout = useCallback((e: LayoutChangeEvent) => {
        updatePostInputTop(e.nativeEvent.layout.height);
    }, []);

    // Listen for emoji picker events
    useEffect(() => {
        const showListener = DeviceEventEmitter.addListener('EMOJI_PICKER_SHOW', () => {
            setEmojiPickerVisible(true);
        });

        const hideListener = DeviceEventEmitter.addListener(Events.CLOSE_BOTTOM_SHEET, () => {
            setEmojiPickerVisible(false);
        });

        return () => {
            showListener.remove();
            hideListener.remove();
        };
    }, []);

    // Animate text input position based on emoji picker state
    useEffect(() => {
        if (emojiPickerVisible && keyboardHeight > 0) {
            // Move text input up when emoji picker is visible and keyboard-sized
            const moveUpDistance = Math.min(keyboardHeight * 0.3, 100); // Move up by 30% of keyboard height, max 100px
            Animated.spring(translateY, {
                toValue: -moveUpDistance,
                tension: 100,
                friction: 8,
                useNativeDriver: true,
            }).start();
        } else {
            // Reset position when emoji picker is hidden
            Animated.spring(translateY, {
                toValue: 0,
                tension: 100,
                friction: 8,
                useNativeDriver: true,
            }).start();
        }
    }, [emojiPickerVisible, keyboardHeight, translateY]);

    const inputRef = useRef<PasteInputRef>();
    const focus = useCallback(() => {
        inputRef.current?.focus();
    }, []);



    // Render
    const postInputTestID = `${testID}.post.input`;
    const quickActionsTestID = `${testID}.quick_actions`;
    const sendActionTestID = `${testID}.send_action`;
    const style = getStyleSheet(theme);

    const persistentNotificationsEnabled = postPriority.persistent_notifications && postPriority.priority === PostPriorityType.URGENT;
    const { noMentionsError, mentionsList } = useMemo(() => {
        let error = false;
        let mentions: string[] = [];
        if (
            channelType !== General.DM_CHANNEL &&
            persistentNotificationsEnabled
        ) {
            mentions = (value.match(MENTIONS_REGEX) || []);
            error = mentions.length === 0;
        }

        return { noMentionsError: error, mentionsList: mentions };
    }, [channelType, persistentNotificationsEnabled, value]);

    const { showAlert } = useAlert();

    const handleSendMessage = useCallback(async () => {
        if (persistentNotificationsEnabled) {
            const result = persistentNotificationsConfirmation_custom(serverUrl, value, mentionsList, intl, sendMessage, persistentNotificationMaxRecipients, persistentNotificationInterval, currentUserId, channelName, channelType);
            result.then((data) => {
                showAlert(data)
            })
        } else {
            sendMessage();
        }
    }, [serverUrl, mentionsList, persistentNotificationsEnabled, persistentNotificationMaxRecipients, sendMessage, value, channelType]);

    const sendActionDisabled = !canSend || noMentionsError;

    const [isPressing, setIsPressing] = useState(false);

    const [isHasFile, changeState] = useState(false);



    const isTablet = useIsTablet();

    const callsConfig = getCallsConfig(serverUrl);
    let callsAvailable = callsConfig.pluginEnabled && groupCallsAllowed;
    if (!groupCallsAllowed && channelType !== General.DM_CHANNEL) {
        callsAvailable = false;
    }


    const onTitlePress = useCallback(preventDoubleTap(() => {
        let title;
        switch (channelType) {
            case General.DM_CHANNEL:
                title = intl.formatMessage({ id: 'screens.channel_info.dm', defaultMessage: 'Direct message info' });
                break;
            case General.GM_CHANNEL:
                title = intl.formatMessage({ id: 'screens.channel_info.gm', defaultMessage: 'Group message info' });
                break;
            default:
                title = intl.formatMessage({ id: 'screens.channel_info', defaultMessage: 'Channel info' });
                break;
        }

        const closeButton = CompassIcon.getImageSourceSync('close', 24, theme.sidebarHeaderTextColor);
        const closeButtonId = 'close-channel-info';

        const options = {
            topBar: {
                leftButtons: [{
                    id: closeButtonId,
                    icon: closeButton,
                    testID: 'close.channel_info.button',
                }],
            },
        };
        showModal(Screens.CHANNEL_INFO, title, { channelType, closeButtonId }, options);
    }), [channelId, channelType, intl, theme]);

    const isDMorGM = isTypeDMorGM(channelType);



    const onChannelQuickAction = useCallback(() => {
        if (isTablet) {
            onTitlePress();
            return;
        }

        const items = callsAvailable && !isDMorGM ? 3 : 2;
        const height = 45 + SEPARATOR_HEIGHT + (items * ITEM_HEIGHT);

        const renderContent = () => {
            return (

                <QuickActions
                    channelId={channelId}
                    callsEnabled={callsAvailable}
                    isDMorGM={isDMorGM}
                />
            );
        };

        bottomSheet({
            title: '',
            renderContent,
            snapPoints: [1, bottomSheetSnapPoint(1, 180, height)],
            theme,
            closeButtonId: 'close-channel-quick-actions',
        });
    }, [isDMorGM, isTablet, onTitlePress, theme, callsAvailable]);



    const handleAddReaction = useCallback(() => {
        // Store original value and cursor position for preview
        const originalValue = value;
        const originalCursor = cursorPosition;

        // Emit event to show emoji picker for positioning
        DeviceEventEmitter.emit('EMOJI_PICKER_SHOW');

        openAsBottomSheet({
            closeButtonId: 'close-add-reaction',
            screen: Screens.EMOJI_PICKER,
            theme,
            title: "",
            props: {
                onEmojiPress: (emoji: string) => {
                    // For single emoji selection, insert directly
                    const emojiDataMew = getEmojiByName(emoji, []);
                    let emojiCharacter = `:${emoji}:`;

                    if (emojiDataMew?.image && emojiDataMew.category !== 'custom') {
                        const codeArray: string[] = emojiDataMew.image.split('-');
                        const code = codeArray.reduce((acc, c) => {
                            return acc + String.fromCodePoint(parseInt(c, 16));
                        }, '');
                        emojiCharacter = code;
                    }

                    // Insert emoji at cursor position
                    const currentValue = value;
                    const currentCursor = cursorPosition;
                    const beforeCursor = currentValue.substring(0, currentCursor);
                    const afterCursor = currentValue.substring(currentCursor);
                    const newValue = beforeCursor + emojiCharacter + afterCursor;

                    updateValue(newValue);
                    updateCursorPosition(currentCursor + emojiCharacter.length);
                },
                isSelectingMultiple: true,
                onEmojiSelectionChange: (selectedEmojis: SelectedEmoji[]) => {
                    // Real-time preview: show selected emojis in text input
                    if (selectedEmojis.length > 0) {
                        const emojiText = selectedEmojis.map(emoji => emoji.character).join(' ');
                        const beforeCursor = originalValue.substring(0, originalCursor);
                        const afterCursor = originalValue.substring(originalCursor);
                        const previewValue = beforeCursor + emojiText + afterCursor;

                        updateValue(previewValue);
                        updateCursorPosition(originalCursor + emojiText.length);
                    } else {
                        // Reset to original value when no emojis selected
                        updateValue(originalValue);
                        updateCursorPosition(originalCursor);
                    }
                },
                onClose: (selectedEmojis: SelectedEmoji[]) => {
                    // Final insertion when picker closes
                    if (selectedEmojis.length > 0) {
                        const emojiText = selectedEmojis.map(emoji => emoji.character).join(' ');
                        const beforeCursor = originalValue.substring(0, originalCursor);
                        const afterCursor = originalValue.substring(originalCursor);
                        const newValue = beforeCursor + emojiText + afterCursor;

                        updateValue(newValue);
                        updateCursorPosition(originalCursor + emojiText.length);
                    } else {
                        // Reset to original value if no emojis selected
                        updateValue(originalValue);
                        updateCursorPosition(originalCursor);
                    }
                }
            },
        });
    }, [theme, value, cursorPosition, updateValue, updateCursorPosition]);







    return (
        <Animated.View
            style={{
                marginHorizontal: 14, marginBottom: 14,
                // ,backgroundColor:'green',
                zIndex: 20,
                transform: [{ translateY }]
            }}
        >
            <Typing
                channelId={channelId}
                rootId={rootId}
            />
            <SafeAreaView
                edges={SAFE_AREA_VIEW_EDGES}
                onLayout={handleLayout}
                style={style.inputWrapper}
                testID={testID}
            >
                <ScrollView
                    style={style.inputContainer}
                    contentContainerStyle={style.inputContentContainer}
                    keyboardShouldPersistTaps={'always'}
                    scrollEnabled={false}
                    showsVerticalScrollIndicator={false}
                    showsHorizontalScrollIndicator={false}
                    pinchGestureEnabled={false}
                    overScrollMode={'never'}
                    disableScrollViewPanResponder={true}
                >
                    <Header
                        noMentionsError={noMentionsError}
                        postPriority={postPriority}
                    />
                    <View style={style.textArea}>


                        <SendAction
                            pageName={pageName}
                            translationY={translationY}
                            isVisibale={isVisibale}
                            addFiles={addFiles}
                            // changeRecodingState={ changeRecodingState}
                            // sendingFile ={sendingFile}
                            currentUserID={currentUserId}
                            rootID={rootId}
                            channelId={channelId}
                            //uploadFile={updateValue}
                            isHadFile={isHasFile}
                            isPressing={isPressing}
                            setIsPressing={setIsPressing}
                            wordLength={value.length}
                            testID={sendActionTestID}
                            disabled={sendActionDisabled}
                            sendMessage={handleSendMessage}
                        />
                        {
                            !isPressing
                            && <View
                                style={{
                                    flex: 1
                                    ,
                                }}
                            >
                                <PostInput
                                    testID={postInputTestID}
                                    channelId={channelId}
                                    maxMessageLength={maxMessageLength}
                                    rootId={rootId}
                                    cursorPosition={cursorPosition}
                                    updateCursorPosition={updateCursorPosition}
                                    updateValue={updateValue}
                                    value={value}
                                    addFiles={addFiles}
                                    sendMessage={handleSendMessage}
                                    inputRef={inputRef}
                                    setIsFocused={setIsFocused}
                                />
                            </View>}
                        <View style={{ height: "100%" }}>
                            <TouchableOpacity key='addReaction' onPress={handleAddReaction}>
                                <CompassIcon
                                    name='emoticon-plus-outline'
                                    size={24}
                                    color={changeOpacity(theme.centerChannelColor, 0.64)}
                                />
                            </TouchableOpacity>
                            {/* <EmojiSelector
                            
                                category={Categories.symbols}
                                onEmojiSelected={emoji => updateValue(prev=>prev+=`${emoji}`)}
                            /> */}
                        </View>
                    </View>
                    <Uploads
                    fileNotBelongToChannel={fileNotBelongToChannel}
                        currentUser={currentUser}
                        changeState={changeState}
                        currentUserId={currentUserId}
                        files={files}
                        uploadFileError={uploadFileError}
                        channelId={channelId}
                        rootId={rootId}
                    />

                    <View style={{ marginHorizontal: 'auto', height: 1, width: '90%', backgroundColor: changeOpacity(theme.sidebarText, 0.1) }}></View>

                    <View style={style.actionsContainer}>
                        <QuickActinoHOlder.default
                            onChannelQuickAction={onChannelQuickAction}
                            channelID={channelId}
                            channelTyp={channelType}
                            groupCallsAllowed={groupCallsAllowed}
                            testID={quickActionsTestID}
                            fileCount={files.length}
                            addFiles={addFiles}
                            updateValue={updateValue}
                            value={value}
                            postPriority={postPriority}
                            updatePostPriority={updatePostPriority}
                            canShowPostPriority={canShowPostPriority}
                            focus={focus}
                        />

                    </View>
                </ScrollView>
            </SafeAreaView>
        </Animated.View>
    );
}
