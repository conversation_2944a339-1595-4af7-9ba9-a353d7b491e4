// Test component to verify emoji picker functionality
// This is a temporary test file to validate the new emoji selection workflow

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';
import { openAsBottomSheet } from '@screens/navigation';
import { Screens } from '@constants';
import { useTheme } from '@context/theme';
import { getEmojiByName } from '@utils/emoji/helpers';
import type { SelectedEmoji } from './emoji_preview';

const EmojiPickerTest = () => {
    const theme = useTheme();
    const [text, setText] = useState('Hello world! ');
    const [cursorPosition, setCursorPosition] = useState(text.length);

    const handleEmojiPickerOpen = () => {
        // Store original values for preview functionality
        const originalText = text;
        const originalCursor = cursorPosition;

        openAsBottomSheet({
            closeButtonId: 'close-emoji-test',
            screen: Screens.EMOJI_PICKER,
            theme,
            title: "Select Emojis",
            props: {
                onEmojiPress: (emoji: string) => {
                    // For single emoji selection, insert directly
                    const emojiDataMew = getEmojiByName(emoji, []);
                    let emojiCharacter = `:${emoji}:`;

                    if (emojiDataMew?.image && emojiDataMew.category !== 'custom') {
                        const codeArray: string[] = emojiDataMew.image.split('-');
                        const code = codeArray.reduce((acc, c) => {
                            return acc + String.fromCodePoint(parseInt(c, 16));
                        }, '');
                        emojiCharacter = code;
                    }

                    // Insert emoji at cursor position
                    const beforeCursor = text.substring(0, cursorPosition);
                    const afterCursor = text.substring(cursorPosition);
                    const newText = beforeCursor + emojiCharacter + afterCursor;

                    setText(newText);
                    setCursorPosition(cursorPosition + emojiCharacter.length);
                },
                isSelectingMultiple: true,
                onEmojiSelectionChange: (selectedEmojis: SelectedEmoji[]) => {
                    // Real-time preview: show selected emojis in text input
                    if (selectedEmojis.length > 0) {
                        const emojiText = selectedEmojis.map(emoji => emoji.character).join(' ');
                        const beforeCursor = originalText.substring(0, originalCursor);
                        const afterCursor = originalText.substring(originalCursor);
                        const previewText = beforeCursor + emojiText + afterCursor;

                        setText(previewText);
                        setCursorPosition(originalCursor + emojiText.length);
                    } else {
                        // Reset to original text when no emojis selected
                        setText(originalText);
                        setCursorPosition(originalCursor);
                    }
                },
                onClose: (selectedEmojis: SelectedEmoji[]) => {
                    // Final insertion when picker closes
                    if (selectedEmojis.length > 0) {
                        const emojiText = selectedEmojis.map(emoji => emoji.character).join(' ');
                        const beforeCursor = originalText.substring(0, originalCursor);
                        const afterCursor = originalText.substring(originalCursor);
                        const newText = beforeCursor + emojiText + afterCursor;

                        setText(newText);
                        setCursorPosition(originalCursor + emojiText.length);
                    } else {
                        // Reset to original text if no emojis selected
                        setText(originalText);
                        setCursorPosition(originalCursor);
                    }
                }
            },
        });
    };

    const styles = StyleSheet.create({
        container: {
            padding: 20,
            backgroundColor: theme.centerChannelBg,
        },
        title: {
            fontSize: 18,
            fontWeight: 'bold',
            color: theme.centerChannelColor,
            marginBottom: 20,
        },
        textInput: {
            borderWidth: 1,
            borderColor: theme.centerChannelColor,
            borderRadius: 8,
            padding: 12,
            fontSize: 16,
            color: theme.centerChannelColor,
            backgroundColor: theme.centerChannelBg,
            minHeight: 100,
            textAlignVertical: 'top',
            marginBottom: 20,
        },
        button: {
            backgroundColor: theme.buttonBg,
            padding: 12,
            borderRadius: 8,
            alignItems: 'center',
        },
        buttonText: {
            color: theme.buttonColor,
            fontSize: 16,
            fontWeight: '600',
        },
        info: {
            fontSize: 14,
            color: theme.centerChannelColor,
            marginBottom: 10,
            opacity: 0.7,
        },
    });

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Emoji Picker Test</Text>
            <Text style={styles.info}>
                Test the enhanced emoji functionality:
                {'\n'}1. Tap "Open Emoji Picker" - should match keyboard height
                {'\n'}2. Select multiple emojis - should see REAL-TIME preview in text input
                {'\n'}3. Notice smooth animations when selecting emojis
                {'\n'}4. Text input should move up dynamically when picker is keyboard-sized
                {'\n'}5. Tap "Done" to finalize insertion
                {'\n'}6. All transitions should be smooth without jarring jumps
            </Text>
            <TextInput
                style={styles.textInput}
                value={text}
                onChangeText={setText}
                onSelectionChange={(event) => {
                    setCursorPosition(event.nativeEvent.selection.start);
                }}
                multiline
                placeholder="Type here and test emoji insertion..."
                placeholderTextColor={theme.centerChannelColor + '80'}
            />
            <TouchableOpacity style={styles.button} onPress={handleEmojiPickerOpen}>
                <Text style={styles.buttonText}>Open Emoji Picker</Text>
            </TouchableOpacity>
        </View>
    );
};

export default EmojiPickerTest;
