// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import {emojiPerformanceMonitor} from './emoji_performance_monitor';

interface TestResult {
    testName: string;
    passed: boolean;
    averageTime: number;
    maxAcceptableTime: number;
    details?: string;
}

class EmojiPerformanceTest {
    private results: TestResult[] = [];

    async runAllTests(): Promise<TestResult[]> {
        console.log('[EmojiPerformanceTest] Starting performance tests...');
        
        // Clear previous metrics
        emojiPerformanceMonitor.clearMetrics();
        
        // Run individual tests
        await this.testEmojiSelection();
        await this.testEmojiPreview();
        await this.testTextInputUpdate();
        await this.testAnimationPerformance();
        
        // Log summary
        this.logResults();
        emojiPerformanceMonitor.logSummary();
        
        return this.results;
    }

    private async testEmojiSelection(): Promise<void> {
        const testName = 'Emoji Selection Performance';
        const maxAcceptableTime = 50; // 50ms for emoji selection
        
        // Simulate multiple emoji selections
        for (let i = 0; i < 10; i++) {
            const operationId = emojiPerformanceMonitor.startOperation('emoji_selection');
            
            // Simulate emoji processing time
            await this.simulateWork(10);
            
            emojiPerformanceMonitor.endOperation(operationId);
        }
        
        const averageTime = emojiPerformanceMonitor.getAverageTime('emoji_selection');
        const passed = averageTime <= maxAcceptableTime;
        
        this.results.push({
            testName,
            passed,
            averageTime,
            maxAcceptableTime,
            details: passed ? 'Within acceptable limits' : 'Performance degraded - consider optimization'
        });
    }

    private async testEmojiPreview(): Promise<void> {
        const testName = 'Emoji Preview Performance';
        const maxAcceptableTime = 100; // 100ms for preview updates
        
        // Simulate multiple preview updates
        for (let i = 0; i < 15; i++) {
            const operationId = emojiPerformanceMonitor.startOperation('emoji_preview');
            
            // Simulate preview rendering time
            await this.simulateWork(20);
            
            emojiPerformanceMonitor.endOperation(operationId);
        }
        
        const averageTime = emojiPerformanceMonitor.getAverageTime('emoji_preview');
        const passed = averageTime <= maxAcceptableTime;
        
        this.results.push({
            testName,
            passed,
            averageTime,
            maxAcceptableTime,
            details: passed ? 'Real-time preview performing well' : 'Preview updates too slow - may impact UX'
        });
    }

    private async testTextInputUpdate(): Promise<void> {
        const testName = 'Text Input Update Performance';
        const maxAcceptableTime = 50; // 50ms for text input updates
        
        // Simulate multiple text input updates
        for (let i = 0; i < 20; i++) {
            const operationId = emojiPerformanceMonitor.startOperation('text_input_update');
            
            // Simulate text input processing time
            await this.simulateWork(5);
            
            emojiPerformanceMonitor.endOperation(operationId);
        }
        
        const averageTime = emojiPerformanceMonitor.getAverageTime('text_input_update');
        const passed = averageTime <= maxAcceptableTime;
        
        this.results.push({
            testName,
            passed,
            averageTime,
            maxAcceptableTime,
            details: passed ? 'Text updates responsive' : 'Text input updates lagging'
        });
    }

    private async testAnimationPerformance(): Promise<void> {
        const testName = 'Animation Performance';
        const maxAcceptableTime = 200; // 200ms for animations
        
        // Simulate multiple animations
        for (let i = 0; i < 8; i++) {
            const operationId = emojiPerformanceMonitor.startOperation('animation');
            
            // Simulate animation processing time
            await this.simulateWork(50);
            
            emojiPerformanceMonitor.endOperation(operationId);
        }
        
        const averageTime = emojiPerformanceMonitor.getAverageTime('animation');
        const passed = averageTime <= maxAcceptableTime;
        
        this.results.push({
            testName,
            passed,
            averageTime,
            maxAcceptableTime,
            details: passed ? 'Smooth animations' : 'Animations may appear choppy'
        });
    }

    private simulateWork(ms: number): Promise<void> {
        return new Promise(resolve => {
            const start = performance.now();
            while (performance.now() - start < ms) {
                // Simulate CPU work
                Math.random();
            }
            resolve();
        });
    }

    private logResults(): void {
        console.log('\n[EmojiPerformanceTest] Test Results:');
        console.log('=====================================');
        
        this.results.forEach(result => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.testName}`);
            console.log(`   Average: ${result.averageTime.toFixed(2)}ms (Max: ${result.maxAcceptableTime}ms)`);
            console.log(`   Details: ${result.details}`);
            console.log('');
        });
        
        const passedTests = this.results.filter(r => r.passed).length;
        const totalTests = this.results.length;
        
        console.log(`Overall: ${passedTests}/${totalTests} tests passed`);
        
        if (passedTests === totalTests) {
            console.log('🎉 All performance tests passed! Emoji functionality is optimized for Redmi devices.');
        } else {
            console.log('⚠️  Some performance tests failed. Consider optimizations for better Redmi device performance.');
        }
    }

    getFailedTests(): TestResult[] {
        return this.results.filter(r => !r.passed);
    }

    getAllTestsPassed(): boolean {
        return this.results.every(r => r.passed);
    }
}

export const emojiPerformanceTest = new EmojiPerformanceTest();

// Helper function to run tests in development
export const runEmojiPerformanceTests = async (): Promise<boolean> => {
    if (__DEV__) {
        const results = await emojiPerformanceTest.runAllTests();
        return emojiPerformanceTest.getAllTestsPassed();
    }
    return true;
};
